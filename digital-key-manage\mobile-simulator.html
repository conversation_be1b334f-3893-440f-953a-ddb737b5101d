<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机端4G控车模拟器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .button {
            padding: 15px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s;
        }
        .connect-btn {
            background-color: #28a745;
            color: white;
        }
        .connect-btn:hover {
            background-color: #218838;
            transform: translateY(-2px);
        }
        .disconnect-btn {
            background-color: #dc3545;
            color: white;
        }
        .disconnect-btn:hover {
            background-color: #c82333;
            transform: translateY(-2px);
        }
        .unlock-btn {
            background-color: #17a2b8;
            color: white;
            font-size: 20px;
        }
        .unlock-btn:hover {
            background-color: #138496;
            transform: translateY(-2px);
        }
        .lock-btn {
            background-color: #ffc107;
            color: #212529;
            font-size: 20px;
        }
        .lock-btn:hover {
            background-color: #e0a800;
            transform: translateY(-2px);
        }
        .log-panel {
            background-color: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            height: 250px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .vehicle-status {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            background-color: #e9ecef;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 手机端4G控车模拟器</h1>
        
        <div class="status-panel">
            <h3>连接状态</h3>
            <p>
                <span id="connectionStatus" class="status-indicator status-disconnected"></span>
                <span id="connectionText">未连接</span>
            </p>
        </div>

        <div class="vehicle-status" id="vehicleStatus">
            🚗 等待车辆状态反馈...
        </div>

        <div class="input-group">
            <label for="serverUrl">WebSocket服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:9201/websocket/message?userId=1001" placeholder="WebSocket URL">
        </div>

        <div class="control-panel">
            <button class="button connect-btn" onclick="connectToServer()">连接服务器</button>
            <button class="button disconnect-btn" onclick="disconnectFromServer()">断开连接</button>
        </div>

        <div class="control-panel">
            <button class="button unlock-btn" onclick="send4GUnlockCommand()">🔓 4G解锁</button>
            <button class="button lock-btn" onclick="send4GLockCommand()">🔒 4G闭锁</button>
        </div>

        <div class="log-panel" id="logPanel">
            <div>手机端4G控车模拟器启动...</div>
            <div>等待连接到WebSocket服务器...</div>
        </div>
    </div>

    <script>
        let socket = null;
        let isConnected = false;

        // 日志函数
        function log(message) {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 更新连接状态显示
        function updateConnectionStatus(connected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = '已连接';
                isConnected = true;
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = '未连接';
                isConnected = false;
            }
        }

        // 更新车辆状态显示
        function updateVehicleStatus(message) {
            const vehicleStatusDiv = document.getElementById('vehicleStatus');
            vehicleStatusDiv.textContent = `🚗 ${message}`;
        }

        // 连接到WebSocket服务器
        function connectToServer() {
            if (isConnected) {
                log('已经连接到服务器');
                return;
            }

            const serverUrl = document.getElementById('serverUrl').value;
            
            log(`尝试连接到 ${serverUrl}...`);
            
            try {
                socket = new WebSocket(serverUrl);
                
                socket.onopen = function(event) {
                    log('成功连接到WebSocket服务器');
                    updateConnectionStatus(true);
                };
                
                socket.onmessage = function(event) {
                    log(`收到服务器消息: ${event.data}`);
                    handleServerMessage(event.data);
                };
                
                socket.onclose = function(event) {
                    log('与服务器连接已断开');
                    updateConnectionStatus(false);
                    socket = null;
                };
                
                socket.onerror = function(error) {
                    log(`连接错误: ${error}`);
                    updateConnectionStatus(false);
                };
                
            } catch (error) {
                log(`连接失败: ${error.message}`);
            }
        }

        // 断开连接
        function disconnectFromServer() {
            if (socket) {
                socket.close();
                socket = null;
                log('主动断开连接');
                updateConnectionStatus(false);
            }
        }

        // 处理服务器消息
        function handleServerMessage(message) {
            try {
                const data = JSON.parse(message);
                
                if (data.senderName === 'TBOX') {
                    // 这是来自TBOX的状态反馈
                    updateVehicleStatus(data.message);
                    log(`车辆状态更新: ${data.message}`);
                } else if (data.senderName === '系统') {
                    // 这是系统响应
                    updateVehicleStatus(data.message);
                    log(`系统响应: ${data.message}`);
                }
            } catch (e) {
                log(`解析消息失败: ${message}`);
            }
        }

        // 发送4G解锁指令
        function send4GUnlockCommand() {
            if (!isConnected || !socket) {
                log('未连接到服务器，无法发送指令');
                return;
            }

            const message = {
                senderName: "手机用户",
                message: "4G_CONTROL unlock",
                messageType: 0
            };

            socket.send(JSON.stringify(message));
            log('发送4G解锁指令');
        }

        // 发送4G闭锁指令
        function send4GLockCommand() {
            if (!isConnected || !socket) {
                log('未连接到服务器，无法发送指令');
                return;
            }

            const message = {
                senderName: "手机用户",
                message: "4G_CONTROL lock",
                messageType: 0
            };

            socket.send(JSON.stringify(message));
            log('发送4G闭锁指令');
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            log('手机端4G控车模拟器已准备就绪');
        };

        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (socket) {
                socket.close();
            }
        };
    </script>
</body>
</html>
