package com.ruoyi.framework.tcp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * TCP控制服务
 * 用于生成和解析TBOX控制指令
 * 
 * <AUTHOR>
 */
@Service
public class TcpControlService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TcpControlService.class);
    
    // 指令模板：7E000A000A000102030405060708090A000030333032303030313034007E
    // 解锁指令：倒数第五位是4
    // 闭锁指令：倒数第五位是3
    private static final String COMMAND_TEMPLATE = "7E000A000A000102030405060708090A00003033303230303031303%s007E";
    
    /**
     * 生成解锁指令
     */
    public String generateUnlockCommand() {
        String command = String.format(COMMAND_TEMPLATE, "4");
        LOGGER.info("生成解锁指令: {}", command);
        return command;
    }
    
    /**
     * 生成闭锁指令
     */
    public String generateLockCommand() {
        String command = String.format(COMMAND_TEMPLATE, "3");
        LOGGER.info("生成闭锁指令: {}", command);
        return command;
    }
    
    /**
     * 解析TBOX状态消息
     * @param message TBOX发送的消息
     * @return 解析结果
     */
    public TboxStatusResult parseTboxMessage(String message) {
        TboxStatusResult result = new TboxStatusResult();
        
        try {
            if (message == null || !message.startsWith("7E") || !message.endsWith("7E")) {
                result.setValid(false);
                result.setErrorMessage("消息格式不正确：必须以7E开头和结尾");
                return result;
            }
            
            if (message.length() < 50) {
                result.setValid(false);
                result.setErrorMessage("消息长度不足");
                return result;
            }
            
            // 提取倒数第五位状态码
            String statusCode = message.substring(message.length() - 6, message.length() - 5);
            
            result.setValid(true);
            result.setOriginalMessage(message);
            result.setStatusCode(statusCode);
            
            switch (statusCode) {
                case "4":
                    result.setLockStatus("unlocked");
                    result.setStatusDescription("车辆已解锁");
                    break;
                case "3":
                    result.setLockStatus("locked");
                    result.setStatusDescription("车辆已闭锁");
                    break;
                default:
                    result.setLockStatus("unknown");
                    result.setStatusDescription("未知状态");
                    break;
            }
            
            LOGGER.info("解析TBOX消息成功: 状态={}, 描述={}", result.getLockStatus(), result.getStatusDescription());
            
        } catch (Exception e) {
            result.setValid(false);
            result.setErrorMessage("解析消息时发生错误: " + e.getMessage());
            LOGGER.error("解析TBOX消息失败: {}", e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 发送解锁指令给所有TBOX
     */
    public void sendUnlockCommandToAllTbox() {
        String command = generateUnlockCommand();
        TcpClientHandler.sendCommandToAllTbox(command);
        LOGGER.info("已向所有TBOX发送解锁指令");
    }
    
    /**
     * 发送闭锁指令给所有TBOX
     */
    public void sendLockCommandToAllTbox() {
        String command = generateLockCommand();
        TcpClientHandler.sendCommandToAllTbox(command);
        LOGGER.info("已向所有TBOX发送闭锁指令");
    }
    
    /**
     * 发送解锁指令给指定TBOX
     */
    public void sendUnlockCommandToTbox(String tboxId) {
        String command = generateUnlockCommand();
        TcpClientHandler.sendCommandToTbox(tboxId, command);
        LOGGER.info("已向TBOX {} 发送解锁指令", tboxId);
    }
    
    /**
     * 发送闭锁指令给指定TBOX
     */
    public void sendLockCommandToTbox(String tboxId) {
        String command = generateLockCommand();
        TcpClientHandler.sendCommandToTbox(tboxId, command);
        LOGGER.info("已向TBOX {} 发送闭锁指令", tboxId);
    }
    
    /**
     * 获取当前连接的TBOX数量
     */
    public int getConnectedTboxCount() {
        return TcpClientHandler.getTboxClients().size();
    }
    
    /**
     * TBOX状态解析结果
     */
    public static class TboxStatusResult {
        private boolean valid;
        private String originalMessage;
        private String statusCode;
        private String lockStatus;
        private String statusDescription;
        private String errorMessage;
        
        // Getters and Setters
        public boolean isValid() {
            return valid;
        }
        
        public void setValid(boolean valid) {
            this.valid = valid;
        }
        
        public String getOriginalMessage() {
            return originalMessage;
        }
        
        public void setOriginalMessage(String originalMessage) {
            this.originalMessage = originalMessage;
        }
        
        public String getStatusCode() {
            return statusCode;
        }
        
        public void setStatusCode(String statusCode) {
            this.statusCode = statusCode;
        }
        
        public String getLockStatus() {
            return lockStatus;
        }
        
        public void setLockStatus(String lockStatus) {
            this.lockStatus = lockStatus;
        }
        
        public String getStatusDescription() {
            return statusDescription;
        }
        
        public void setStatusDescription(String statusDescription) {
            this.statusDescription = statusDescription;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
}
