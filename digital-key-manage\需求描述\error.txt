"C:\Program Files\Java\jdk-11.0.5\bin\java.exe" -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:52500,suspend=y,server=n -Drebel.base=C:\Users\<USER>\.jrebel -Drebel.env.ide.plugin.build=79cd0f025e4d098e5ce0fcd02f60463c55984df5 -Drebel.env.ide.plugin.version=2024.4.1 -Drebel.env.ide.version=2022.3.3 -Drebel.env.ide.product=IU -Drebel.env.ide=intellij -Drebel.notification.url=http://localhost:17434 -Xshare:off -agentpath:C:\Users\<USER>\AppData\Roaming\JetBrains\IntelliJIdea2022.3\plugins\jr-ide-idea\lib\jrebel6\lib\jrebel64.dll -XX:TieredStopAtLevel=1 -noverify -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2022.3\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture.props -Dfile.encoding=UTF-8 -classpath "D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-modules\ruoyi-system\target\classes;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2021.0.5.0\spring-cloud-starter-alibaba-nacos-discovery-2021.0.5.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2021.0.5.0\spring-cloud-alibaba-commons-2021.0.5.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-client\2.2.0\nacos-client-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-auth-plugin\2.2.0\nacos-auth-plugin-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-encryption-plugin\2.2.0\nacos-encryption-plugin-2.2.0.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.15.0\simpleclient-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel\0.15.0\simpleclient_tracer_otel-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_common\0.15.0\simpleclient_tracer_common-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel_agent\0.15.0\simpleclient_tracer_otel_agent-0.15.0.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\3.1.7\spring-cloud-commons-3.1.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\3.1.7\spring-cloud-context-3.1.7.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2021.0.5.0\spring-cloud-starter-alibaba-nacos-config-2021.0.5.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-sentinel\2021.0.5.0\spring-cloud-starter-alibaba-sentinel-2021.0.5.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-transport-simple-http\1.8.6\sentinel-transport-simple-http-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-transport-common\1.8.6\sentinel-transport-common-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-datasource-extension\1.8.6\sentinel-datasource-extension-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.83_noneautotype\fastjson-1.2.83_noneautotype.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-annotation-aspectj\1.8.6\sentinel-annotation-aspectj-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-core\1.8.6\sentinel-core-1.8.6.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-circuitbreaker-sentinel\2021.0.5.0\spring-cloud-circuitbreaker-sentinel-2021.0.5.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-reactor-adapter\1.8.6\sentinel-reactor-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-spring-webflux-adapter\1.8.6\sentinel-spring-webflux-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-spring-webmvc-adapter\1.8.6\sentinel-spring-webmvc-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-parameter-flow-control\1.8.6\sentinel-parameter-flow-control-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\googlecode\concurrentlinkedhashmap\concurrentlinkedhashmap-lru\1.4.2\concurrentlinkedhashmap-lru-1.4.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-server-default\1.8.6\sentinel-cluster-server-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-common-default\1.8.6\sentinel-cluster-common-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-client-default\1.8.6\sentinel-cluster-client-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-alibaba-sentinel-datasource\2021.0.5.0\spring-cloud-alibaba-sentinel-datasource-2021.0.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.7.18\spring-boot-starter-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.33\spring-core-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.33\spring-jcl-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.7.18\spring-boot-actuator-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.7.18\spring-boot-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.9.17\micrometer-core-1.9.17.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.33\spring-aop-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-common\ruoyi-common-datasource\target\classes;C:\Users\<USER>\.m2\repository\com\alibaba\druid-spring-boot-starter\1.2.20\druid-spring-boot-starter-1.2.20.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid\1.2.20\druid-1.2.20.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.9\jaxb-runtime-2.3.9.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.9\txw2-2.3.9.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\dynamic-datasource-spring-boot-starter\4.2.0\dynamic-datasource-spring-boot-starter-4.2.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\dynamic-datasource-spring-boot-common\4.2.0\dynamic-datasource-spring-boot-common-4.2.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\dynamic-datasource-spring\4.2.0\dynamic-datasource-spring-4.2.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\dynamic-datasource-creator\4.2.0\dynamic-datasource-creator-4.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.33\spring-jdbc-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.33\spring-tx-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.18\spring-boot-starter-aop-2.7.18.jar;D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-common\ruoyi-common-datascope\target\classes;D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-common\ruoyi-common-security\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.33\spring-webmvc-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.33\spring-expression-5.3.33.jar;D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-api\ruoyi-api-system\target\classes;D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-common\ruoyi-common-core\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-openfeign\3.1.8\spring-cloud-starter-openfeign-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-openfeign-core\3.1.8\spring-cloud-openfeign-core-3.1.8.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-core\11.10\feign-core-11.10.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-slf4j\11.10\feign-slf4j-11.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\3.1.7\spring-cloud-starter-loadbalancer-3.1.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-loadbalancer\3.1.7\spring-cloud-loadbalancer-3.1.7.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\addons\reactor-extra\3.4.10\reactor-extra-3.4.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\2.7.18\spring-boot-starter-cache-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.33\spring-context-support-5.3.33.jar;C:\Users\<USER>\.m2\repository\com\alibaba\transmittable-thread-local\2.14.4\transmittable-thread-local-2.14.4.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-starter\2.0.0\pagehelper-spring-boot-starter-2.0.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.3.1\mybatis-spring-boot-starter-2.3.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.3.1\mybatis-spring-boot-autoconfigure-2.3.1.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.13\mybatis-3.5.13.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.1.1\mybatis-spring-2.1.1.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\2.0.0\pagehelper-spring-boot-autoconfigure-2.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\6.0.0\pagehelper-6.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\4.5\jsqlparser-4.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.18\spring-boot-starter-validation-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson2\fastjson2\2.0.43\fastjson2-2.0.43.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.13.0\commons-io-2.13.0.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\4.1.2\poi-ooxml-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\4.1.2\poi-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\3.1.0\xmlbeans-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.19\commons-compress-1.19.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.06\curvesapi-1.06.jar;C:\Users\<USER>\.m2\repository\javax\servlet\javax.servlet-api\4.0.1\javax.servlet-api-4.0.1.jar;D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-common\ruoyi-common-redis\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.7.18\spring-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.33\spring-oxm-5.3.33.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-common\ruoyi-common-log\target\classes;D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-common\ruoyi-common-swagger\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.33\spring-web-5.3.33.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.6.2\swagger-annotations-1.6.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.6.2\swagger-models-1.6.2.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\2.7.18\spring-boot-starter-websocket-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.3.33\spring-messaging-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.33\spring-beans-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.33\spring-websocket-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.33\spring-context-5.3.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\3.1.7\spring-cloud-starter-bootstrap-3.1.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\3.1.7\spring-cloud-starter-3.1.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.11.RELEASE\spring-security-rsa-1.0.11.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.69\bcpkix-jdk15on-1.69.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.69\bcprov-jdk15on-1.69.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcutil-jdk15on\1.69\bcutil-jdk15on-1.69.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2022.3.3\lib\idea_rt.jar" com.ruoyi.system.RuoYiSystemApplication
Connected to the target VM, address: '127.0.0.1:52500', transport: 'socket'
2025-08-01 20:46:00 JRebel: 
2025-08-01 20:46:00 JRebel: A newer version '2025.3.0' is available for download 
2025-08-01 20:46:00 JRebel: from https://jrebel.com/software/jrebel/download/
2025-08-01 20:46:00 JRebel: 
2025-08-01 20:46:00 JRebel:  Starting logging to file: C:\Users\<USER>\.jrebel\jrebel.log
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  #############################################################
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  JRebel Agent 2024.4.1 (202411041342)
2025-08-01 20:46:00 JRebel:  (c) Copyright 2007-2024 Perforce Software, Inc.
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  Over the last 30 days JRebel prevented
2025-08-01 20:46:00 JRebel:  at least 173 redeploys/restarts saving you about 86.5 hours.
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  Over the last 228 days JRebel prevented
2025-08-01 20:46:00 JRebel:  at least 221 redeploys/restarts saving you about 110.5 hours.
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  License acquired from License Server: https://jrebel.abcde.work
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  Licensed to shuyi.
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  You are using an offline license.
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  
2025-08-01 20:46:00 JRebel:  #############################################################
2025-08-01 20:46:00 JRebel:  
20:46:02.298 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
20:46:02.367 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:46:03.289 [main] ERROR o.s.b.SpringApplication - [reportFailure,818] - Application run failed
org.yaml.snakeyaml.constructor.DuplicateKeyException: while constructing a mapping
 in 'reader', line 35, column 11:
              driver-class-name: com.mysql.cj. ... 
              ^
found duplicate key driver-class-name
 in 'reader', line 40, column 11:
              driver-class-name: org.h2.Driver
              ^

	at org.yaml.snakeyaml.constructor.SafeConstructor.processDuplicateKeys(SafeConstructor.java:105)
	at org.yaml.snakeyaml.constructor.SafeConstructor.flattenMapping(SafeConstructor.java:76)
	at org.yaml.snakeyaml.constructor.SafeConstructor.constructMapping2ndStep(SafeConstructor.java:189)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping(BaseConstructor.java:461)
	at org.yaml.snakeyaml.constructor.SafeConstructor$ConstructYamlMap.construct(SafeConstructor.java:556)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObjectNoCheck(BaseConstructor.java:230)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObject(BaseConstructor.java:220)
	at org.springframework.boot.env.OriginTrackedYamlLoader$OriginTrackingConstructor.constructObject(OriginTrackedYamlLoader.java:123)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping2ndStep(BaseConstructor.java:480)
	at org.yaml.snakeyaml.constructor.SafeConstructor.constructMapping2ndStep(SafeConstructor.java:190)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping(BaseConstructor.java:461)
	at org.yaml.snakeyaml.constructor.SafeConstructor$ConstructYamlMap.construct(SafeConstructor.java:556)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObjectNoCheck(BaseConstructor.java:230)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObject(BaseConstructor.java:220)
	at org.springframework.boot.env.OriginTrackedYamlLoader$OriginTrackingConstructor.constructObject(OriginTrackedYamlLoader.java:123)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping2ndStep(BaseConstructor.java:480)
	at org.yaml.snakeyaml.constructor.SafeConstructor.constructMapping2ndStep(SafeConstructor.java:190)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping(BaseConstructor.java:461)
	at org.yaml.snakeyaml.constructor.SafeConstructor$ConstructYamlMap.construct(SafeConstructor.java:556)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObjectNoCheck(BaseConstructor.java:230)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObject(BaseConstructor.java:220)
	at org.springframework.boot.env.OriginTrackedYamlLoader$OriginTrackingConstructor.constructObject(OriginTrackedYamlLoader.java:123)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping2ndStep(BaseConstructor.java:480)
	at org.yaml.snakeyaml.constructor.SafeConstructor.constructMapping2ndStep(SafeConstructor.java:190)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping(BaseConstructor.java:461)
	at org.yaml.snakeyaml.constructor.SafeConstructor$ConstructYamlMap.construct(SafeConstructor.java:556)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObjectNoCheck(BaseConstructor.java:230)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObject(BaseConstructor.java:220)
	at org.springframework.boot.env.OriginTrackedYamlLoader$OriginTrackingConstructor.constructObject(OriginTrackedYamlLoader.java:123)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping2ndStep(BaseConstructor.java:480)
	at org.yaml.snakeyaml.constructor.SafeConstructor.constructMapping2ndStep(SafeConstructor.java:190)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping(BaseConstructor.java:461)
	at org.yaml.snakeyaml.constructor.SafeConstructor$ConstructYamlMap.construct(SafeConstructor.java:556)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObjectNoCheck(BaseConstructor.java:230)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObject(BaseConstructor.java:220)
	at org.springframework.boot.env.OriginTrackedYamlLoader$OriginTrackingConstructor.constructObject(OriginTrackedYamlLoader.java:123)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping2ndStep(BaseConstructor.java:480)
	at org.yaml.snakeyaml.constructor.SafeConstructor.constructMapping2ndStep(SafeConstructor.java:190)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructMapping(BaseConstructor.java:461)
	at org.yaml.snakeyaml.constructor.SafeConstructor$ConstructYamlMap.construct(SafeConstructor.java:556)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObjectNoCheck(BaseConstructor.java:230)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructObject(BaseConstructor.java:220)
	at org.springframework.boot.env.OriginTrackedYamlLoader$OriginTrackingConstructor.constructObject(OriginTrackedYamlLoader.java:123)
	at org.yaml.snakeyaml.constructor.BaseConstructor.constructDocument(BaseConstructor.java:174)
	at org.yaml.snakeyaml.constructor.BaseConstructor.getData(BaseConstructor.java:139)
	at org.springframework.boot.env.OriginTrackedYamlLoader$OriginTrackingConstructor.getData(OriginTrackedYamlLoader.java:103)
	at org.yaml.snakeyaml.Yaml$1.next(Yaml.java:514)
	at org.springframework.beans.factory.config.YamlProcessor.process(YamlProcessor.java:199)
	at org.springframework.beans.factory.config.YamlProcessor.process(YamlProcessor.java:166)
	at org.springframework.boot.env.OriginTrackedYamlLoader.load(OriginTrackedYamlLoader.java:88)
	at org.springframework.boot.env.YamlPropertySourceLoader.load(YamlPropertySourceLoader.java:50)
	at org.springframework.boot.context.config.StandardConfigDataLoader.load(StandardConfigDataLoader.java:54)
	at org.springframework.boot.context.config.StandardConfigDataLoader.load(StandardConfigDataLoader.java:36)
	at org.springframework.boot.context.config.ConfigDataLoaders.load(ConfigDataLoaders.java:108)
	at org.springframework.boot.context.config.ConfigDataImporter.load(ConfigDataImporter.java:132)
	at org.springframework.boot.context.config.ConfigDataImporter.resolveAndLoad(ConfigDataImporter.java:87)
	at org.springframework.boot.context.config.ConfigDataEnvironmentContributors.withProcessedImports(ConfigDataEnvironmentContributors.java:116)
	at org.springframework.boot.context.config.ConfigDataEnvironment.processWithProfiles(ConfigDataEnvironment.java:311)
	at org.springframework.boot.context.config.ConfigDataEnvironment.processAndApply(ConfigDataEnvironment.java:232)
	at org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor.postProcessEnvironment(ConfigDataEnvironmentPostProcessor.java:102)
	at org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor.postProcessEnvironment(ConfigDataEnvironmentPostProcessor.java:94)
	at org.springframework.boot.env.EnvironmentPostProcessorApplicationListener.onApplicationEnvironmentPreparedEvent(EnvironmentPostProcessorApplicationListener.java:102)
	at org.springframework.boot.env.EnvironmentPostProcessorApplicationListener.onApplicationEvent(EnvironmentPostProcessorApplicationListener.java:87)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:133)
	at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:85)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:66)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1540)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:114)
	at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:65)
	at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:344)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:302)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.system.RuoYiSystemApplication.main(RuoYiSystemApplication.java:24)
Disconnected from the target VM, address: '127.0.0.1:52500', transport: 'socket'

Process finished with exit code 1
