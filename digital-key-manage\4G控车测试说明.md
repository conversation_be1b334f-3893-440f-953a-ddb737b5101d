# 4G控车功能测试说明

## 功能概述

本项目实现了一个完整的4G控车系统，包括：
1. SpringBoot云平台TCP服务器
2. WebSocket与TCP通信集成
3. TBOX模拟器（HTML页面）
4. 手机端模拟器（HTML页面）

## 系统架构

```
手机端(WebSocket) -> 云平台(WebSocket+TCP) -> TBOX(TCP客户端)
                                    ↓
                              状态反馈回传
```

## 测试流程

### 1. 启动SpringBoot服务

确保以下服务正常启动：
- `ruoyi-system` 模块（端口：9201）
- TCP服务器会自动启动在端口9999
- WebSocket服务在 `/websocket/message`
- WebSocket到TCP代理在 `/websocket/tbox`

### 2. 启动TBOX模拟器

1. 打开 `tbox-simulator.html` 文件
2. 确认服务器地址为 `localhost`，端口为 `9999`
3. 点击"连接服务器"按钮
4. 观察连接状态变为"已连接"
5. 初始车辆状态应显示为"闭锁"

### 3. 启动手机端模拟器

1. 打开 `mobile-simulator.html` 文件
2. 确认WebSocket地址为 `ws://localhost:9201/websocket/message?userId=1001`
3. 点击"连接服务器"按钮
4. 观察连接状态变为"已连接"

### 4. 执行4G控车测试

#### 测试解锁功能：
1. 在手机端模拟器中点击"🔓 4G解锁"按钮
2. 观察手机端日志：应显示"发送4G解锁指令"
3. 观察TBOX模拟器：
   - 日志应显示"收到解锁指令"
   - 车辆状态应变为"解锁"
   - 自动发送状态确认
4. 观察手机端：应收到车辆状态反馈

#### 测试闭锁功能：
1. 在手机端模拟器中点击"🔒 4G闭锁"按钮
2. 观察手机端日志：应显示"发送4G闭锁指令"
3. 观察TBOX模拟器：
   - 日志应显示"收到闭锁指令"
   - 车辆状态应变为"闭锁"
   - 自动发送状态确认
4. 观察手机端：应收到车辆状态反馈

## 指令格式说明

### TCP指令格式
- 解锁指令：`7E000A000A000102030405060708090A000030333032303030313034007E`
- 闭锁指令：`7E000A000A000102030405060708090A000030333032303030313033007E`
- 格式说明：倒数第五位是状态码（4=解锁，3=闭锁）

### WebSocket消息格式
```json
{
  "senderName": "手机用户",
  "message": "4G_CONTROL unlock", // 或 "4G_CONTROL lock"
  "messageType": 0
}
```

## 预期测试结果

### 成功场景：
1. 手机端发送4G控车指令
2. 云平台接收WebSocket消息并识别为4G控车请求
3. 云平台生成对应的TCP指令发送给TBOX
4. TBOX接收指令并更新车辆状态
5. TBOX发送状态确认给云平台
6. 云平台通过WebSocket将状态反馈给手机端

### 日志示例：

**手机端日志：**
```
[14:30:01] 发送4G解锁指令
[14:30:02] 收到服务器消息: {"senderName":"系统","message":"解锁指令已发送","messageType":0}
[14:30:03] 收到服务器消息: {"senderName":"TBOX","message":"车辆状态: unlocked","messageType":0}
```

**TBOX日志：**
```
[14:30:02] 收到服务器消息: 7E000A000A000102030405060708090A000030333032303030313034007E
[14:30:02] 收到解锁指令
[14:30:03] 发送车辆状态: unlocked
```

## 故障排除

### 常见问题：

1. **TBOX无法连接**
   - 检查TCP服务器是否启动（端口9999）
   - 检查防火墙设置

2. **手机端无法连接**
   - 检查WebSocket服务是否启动（端口9201）
   - 检查URL格式是否正确

3. **指令无响应**
   - 检查TCP服务器日志
   - 检查WebSocket消息格式
   - 确认TcpControlService是否正确初始化

4. **状态反馈异常**
   - 检查TBOX模拟器是否正确解析指令
   - 检查WebSocket连接是否正常

## 技术实现要点

1. **TCP服务器**：使用Java Socket实现，支持多客户端连接
2. **WebSocket集成**：扩展现有WebSocket服务，添加4G控车消息处理
3. **消息转换**：WebSocket JSON消息转换为TCP二进制指令
4. **状态同步**：双向状态同步机制，确保数据一致性
5. **代理服务**：WebSocket到TCP代理，解决浏览器无法直接连接TCP的问题

## 扩展功能

后续可以扩展的功能：
1. 用户身份验证和权限控制
2. 车辆ID绑定和多车辆支持
3. 指令加密和安全传输
4. 状态持久化和历史记录
5. 实时监控和告警机制
