package com.ruoyi.framework.tcp;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * TCP服务器配置
 * 
 * <AUTHOR>
 */
@Configuration
public class TcpServerConfig {
    
    @Value("${tcp.server.port:9999}")
    private int tcpPort;
    
    @Value("${tcp.server.enabled:true}")
    private boolean tcpEnabled;
    
    public int getTcpPort() {
        return tcpPort;
    }
    
    public boolean isTcpEnabled() {
        return tcpEnabled;
    }
    
    @Bean
    public TcpServer tcpServer() {
        return new TcpServer(tcpPort, tcpEnabled);
    }
}
