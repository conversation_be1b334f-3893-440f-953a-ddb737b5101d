<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TBOX模拟器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .button {
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .connect-btn {
            background-color: #28a745;
            color: white;
        }
        .connect-btn:hover {
            background-color: #218838;
        }
        .disconnect-btn {
            background-color: #dc3545;
            color: white;
        }
        .disconnect-btn:hover {
            background-color: #c82333;
        }
        .lock-btn {
            background-color: #ffc107;
            color: #212529;
        }
        .lock-btn:hover {
            background-color: #e0a800;
        }
        .unlock-btn {
            background-color: #17a2b8;
            color: white;
        }
        .unlock-btn:hover {
            background-color: #138496;
        }
        .log-panel {
            background-color: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .vehicle-status {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .vehicle-locked {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .vehicle-unlocked {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 TBOX模拟器</h1>
        
        <div class="status-panel">
            <h3>连接状态</h3>
            <p>
                <span id="connectionStatus" class="status-indicator status-disconnected"></span>
                <span id="connectionText">未连接</span>
            </p>
        </div>

        <div class="vehicle-status" id="vehicleStatus">
            🔒 车辆状态：闭锁
        </div>

        <div class="input-group">
            <label for="serverHost">服务器地址:</label>
            <input type="text" id="serverHost" value="localhost" placeholder="localhost">
        </div>

        <div class="input-group">
            <label for="serverPort">服务器端口:</label>
            <input type="number" id="serverPort" value="9999" placeholder="9999">
        </div>

        <div class="control-panel">
            <button class="button connect-btn" onclick="connectToServer()">连接服务器</button>
            <button class="button disconnect-btn" onclick="disconnectFromServer()">断开连接</button>
            <button class="button lock-btn" onclick="sendLockStatus()">发送闭锁状态</button>
            <button class="button unlock-btn" onclick="sendUnlockStatus()">发送解锁状态</button>
        </div>

        <div class="log-panel" id="logPanel">
            <div>TBOX模拟器启动...</div>
            <div>等待连接到TCP服务器...</div>
        </div>
    </div>

    <script>
        let socket = null;
        let isConnected = false;
        let currentVehicleStatus = 'locked'; // locked 或 unlocked

        // 日志函数
        function log(message) {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 更新连接状态显示
        function updateConnectionStatus(connected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = '已连接';
                isConnected = true;
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = '未连接';
                isConnected = false;
            }
        }

        // 更新车辆状态显示
        function updateVehicleStatus(status) {
            const vehicleStatusDiv = document.getElementById('vehicleStatus');
            currentVehicleStatus = status;
            
            if (status === 'locked') {
                vehicleStatusDiv.textContent = '🔒 车辆状态：闭锁';
                vehicleStatusDiv.className = 'vehicle-status vehicle-locked';
            } else {
                vehicleStatusDiv.textContent = '🔓 车辆状态：解锁';
                vehicleStatusDiv.className = 'vehicle-status vehicle-unlocked';
            }
        }

        // 连接到TCP服务器
        function connectToServer() {
            if (isConnected) {
                log('已经连接到服务器');
                return;
            }

            const host = document.getElementById('serverHost').value || 'localhost';
            const port = document.getElementById('serverPort').value || '9999';
            
            log(`尝试连接到 ${host}:${port}...`);
            
            // 通过WebSocket代理连接到TCP服务器
            try {
                // 连接到WebSocket代理服务器，代理服务器会转发到TCP服务器
                socket = new WebSocket(`ws://${host}:9201/websocket/tbox`);
                
                socket.onopen = function(event) {
                    log('成功连接到TCP服务器');
                    updateConnectionStatus(true);
                    
                    // 连接成功后发送初始状态
                    setTimeout(() => {
                        sendCurrentStatus();
                    }, 1000);
                };
                
                socket.onmessage = function(event) {
                    log(`收到服务器消息: ${event.data}`);
                    handleServerMessage(event.data);
                };
                
                socket.onclose = function(event) {
                    log('与服务器连接已断开');
                    updateConnectionStatus(false);
                    socket = null;
                };
                
                socket.onerror = function(error) {
                    log(`连接错误: ${error}`);
                    updateConnectionStatus(false);
                };
                
            } catch (error) {
                log(`连接失败: ${error.message}`);
            }
        }

        // 断开连接
        function disconnectFromServer() {
            if (socket) {
                socket.close();
                socket = null;
                log('主动断开连接');
                updateConnectionStatus(false);
            }
        }

        // 处理服务器消息
        function handleServerMessage(message) {
            // 解析TCP指令格式: 7E000A000A000102030405060708090A000030333032303030313034007E
            if (message.startsWith('7E') && message.endsWith('7E') && message.length >= 50) {
                // 提取倒数第五位状态码
                const statusCode = message.substring(message.length - 6, message.length - 5);
                
                if (statusCode === '4') {
                    log('收到解锁指令');
                    updateVehicleStatus('unlocked');
                    // 延迟发送状态确认
                    setTimeout(() => {
                        sendCurrentStatus();
                    }, 500);
                } else if (statusCode === '3') {
                    log('收到闭锁指令');
                    updateVehicleStatus('locked');
                    // 延迟发送状态确认
                    setTimeout(() => {
                        sendCurrentStatus();
                    }, 500);
                } else {
                    log(`收到未知指令，状态码: ${statusCode}`);
                }
            } else {
                log(`收到格式不正确的指令: ${message}`);
            }
        }

        // 发送当前状态
        function sendCurrentStatus() {
            if (!isConnected || !socket) {
                log('未连接到服务器，无法发送状态');
                return;
            }

            const statusCode = currentVehicleStatus === 'locked' ? '3' : '4';
            const statusMessage = generateStatusMessage(statusCode);
            
            socket.send(statusMessage);
            log(`发送车辆状态: ${currentVehicleStatus} (${statusMessage})`);
        }

        // 发送闭锁状态
        function sendLockStatus() {
            updateVehicleStatus('locked');
            sendCurrentStatus();
        }

        // 发送解锁状态
        function sendUnlockStatus() {
            updateVehicleStatus('unlocked');
            sendCurrentStatus();
        }

        // 生成状态消息
        function generateStatusMessage(statusCode) {
            // 使用模板生成消息：7E000A000A000102030405060708090A000030333032303030313034007E
            return `7E000A000A000102030405060708090A00003033303230303031303${statusCode}007E`;
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            log('TBOX模拟器已准备就绪');
            updateVehicleStatus('locked');
        };

        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (socket) {
                socket.close();
            }
        };
    </script>
</body>
</html>
