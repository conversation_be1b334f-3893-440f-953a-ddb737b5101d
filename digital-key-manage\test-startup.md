# 启动测试说明

## 问题修复

已修复Bean定义冲突问题：
- 移除了 `TcpServer.java` 中的 `@Component` 注解
- 保留了 `TcpServerConfig.java` 中的 `@Bean` 方法定义
- 这样避免了重复的Bean定义

## 修复内容

### 原问题：
```
The bean 'tcpServer', defined in class path resource [com/ruoyi/framework/tcp/TcpServerConfig.class], could not be registered. A bean with that name has already been defined in file [D:\Develop\zj-zerosense\digital-key-all\digital-key-manage\ruoyi-modules\ruoyi-system\target\classes\com\ruoyi\framework\tcp\TcpServer.class] and overriding is disabled.
```

### 解决方案：
1. 移除 `TcpServer.java` 中的 `@Component` 注解
2. 保留 `TcpServerConfig.java` 中的 `@Bean` 方法
3. 通过配置类统一管理TCP服务器的创建和配置

## 启动验证

现在可以重新启动 `ruoyi-system` 模块，应该不会再出现Bean冲突错误。

## 测试步骤

1. 启动 SpringBoot 应用
2. 检查TCP服务器是否在端口9999启动
3. 检查WebSocket服务是否正常
4. 使用HTML模拟器进行测试

## 预期结果

- 应用正常启动
- TCP服务器监听端口9999
- WebSocket服务正常工作
- 可以进行完整的4G控车测试流程
