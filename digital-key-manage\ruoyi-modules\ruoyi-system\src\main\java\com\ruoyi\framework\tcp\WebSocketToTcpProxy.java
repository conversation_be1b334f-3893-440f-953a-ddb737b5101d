package com.ruoyi.framework.tcp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.*;
import java.net.Socket;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket到TCP代理服务器
 * 用于HTML页面通过WebSocket连接到TCP服务器
 * 
 * <AUTHOR>
 */
@Component
@ServerEndpoint("/websocket/tbox")
public class WebSocketToTcpProxy {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketToTcpProxy.class);
    
    // 存储WebSocket会话和对应的TCP连接
    private static final ConcurrentHashMap<String, TcpConnection> TCP_CONNECTIONS = new ConcurrentHashMap<>();
    
    /**
     * WebSocket连接建立时调用
     */
    @OnOpen
    public void onOpen(Session session) {
        try {
            String sessionId = session.getId();
            LOGGER.info("WebSocket连接建立: {}", sessionId);
            
            // 创建到TCP服务器的连接
            Socket tcpSocket = new Socket("localhost", 9999);
            TcpConnection tcpConnection = new TcpConnection(tcpSocket, session);
            TCP_CONNECTIONS.put(sessionId, tcpConnection);
            
            // 启动TCP消息监听线程
            new Thread(tcpConnection::listenTcpMessages).start();
            
            LOGGER.info("TCP连接已建立: {}", sessionId);
            
        } catch (Exception e) {
            LOGGER.error("建立TCP连接失败: {}", e.getMessage(), e);
            try {
                session.close();
            } catch (IOException ex) {
                LOGGER.error("关闭WebSocket会话失败: {}", ex.getMessage(), ex);
            }
        }
    }
    
    /**
     * 接收WebSocket消息时调用
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        String sessionId = session.getId();
        TcpConnection tcpConnection = TCP_CONNECTIONS.get(sessionId);
        
        if (tcpConnection != null) {
            tcpConnection.sendToTcp(message);
            LOGGER.info("转发WebSocket消息到TCP: {} -> {}", sessionId, message);
        } else {
            LOGGER.warn("未找到对应的TCP连接: {}", sessionId);
        }
    }
    
    /**
     * WebSocket连接关闭时调用
     */
    @OnClose
    public void onClose(Session session) {
        String sessionId = session.getId();
        TcpConnection tcpConnection = TCP_CONNECTIONS.remove(sessionId);
        
        if (tcpConnection != null) {
            tcpConnection.close();
            LOGGER.info("TCP连接已关闭: {}", sessionId);
        }
        
        LOGGER.info("WebSocket连接已关闭: {}", sessionId);
    }
    
    /**
     * WebSocket连接错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        String sessionId = session.getId();
        LOGGER.error("WebSocket连接错误: {} - {}", sessionId, error.getMessage(), error);
        
        TcpConnection tcpConnection = TCP_CONNECTIONS.remove(sessionId);
        if (tcpConnection != null) {
            tcpConnection.close();
        }
    }
    
    /**
     * TCP连接包装类
     */
    private static class TcpConnection {
        private final Socket tcpSocket;
        private final Session webSocketSession;
        private final BufferedReader tcpReader;
        private final PrintWriter tcpWriter;
        private volatile boolean running = true;
        
        public TcpConnection(Socket tcpSocket, Session webSocketSession) throws IOException {
            this.tcpSocket = tcpSocket;
            this.webSocketSession = webSocketSession;
            this.tcpReader = new BufferedReader(new InputStreamReader(tcpSocket.getInputStream()));
            this.tcpWriter = new PrintWriter(tcpSocket.getOutputStream(), true);
        }
        
        /**
         * 监听TCP消息并转发到WebSocket
         */
        public void listenTcpMessages() {
            try {
                String message;
                while (running && (message = tcpReader.readLine()) != null) {
                    // 转发TCP消息到WebSocket
                    if (webSocketSession.isOpen()) {
                        webSocketSession.getBasicRemote().sendText(message);
                        LOGGER.info("转发TCP消息到WebSocket: {}", message);
                    } else {
                        LOGGER.warn("WebSocket会话已关闭，停止转发TCP消息");
                        break;
                    }
                }
            } catch (IOException e) {
                if (running) {
                    LOGGER.error("监听TCP消息时发生错误: {}", e.getMessage(), e);
                }
            } finally {
                close();
            }
        }
        
        /**
         * 发送消息到TCP服务器
         */
        public void sendToTcp(String message) {
            if (tcpWriter != null && !tcpSocket.isClosed()) {
                tcpWriter.println(message);
            }
        }
        
        /**
         * 关闭连接
         */
        public void close() {
            running = false;
            try {
                if (tcpReader != null) tcpReader.close();
                if (tcpWriter != null) tcpWriter.close();
                if (tcpSocket != null && !tcpSocket.isClosed()) tcpSocket.close();
                if (webSocketSession != null && webSocketSession.isOpen()) {
                    webSocketSession.close();
                }
            } catch (IOException e) {
                LOGGER.error("关闭TCP连接时发生错误: {}", e.getMessage(), e);
            }
        }
    }
}
