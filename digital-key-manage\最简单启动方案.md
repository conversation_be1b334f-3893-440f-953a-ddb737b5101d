# 4G控车功能 - 最简单启动方案

## 🎯 问题分析

从最新错误可以看到：
- ✅ **数据库连接成功**：`{dataSource-1,master} inited`
- ❌ **数据初始化重复**：主键冲突，因为H2每次重启都执行data.sql

## 🚀 最简单解决方案

### 方案1：禁用数据库依赖（推荐）

既然您要求最简单的方式，我们可以暂时禁用所有数据库相关的功能，只保留核心的TCP和WebSocket服务：

1. **修改启动类**，排除数据库自动配置：

```java
@SpringBootApplication(exclude = {
    DataSourceAutoConfiguration.class,
    DataSourceTransactionManagerAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class
})
public class RuoYiSystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(RuoYiSystemApplication.class, args);
    }
}
```

### 方案2：使用内存存储（更简单）

直接在代码中使用HashMap等内存存储，不依赖任何数据库：

```java
// 在TcpControlService中使用内存存储
private static final Map<String, String> DEVICE_STATUS = new ConcurrentHashMap<>();
private static final Map<String, String> USER_VEHICLE = new ConcurrentHashMap<>();

static {
    // 初始化测试数据
    DEVICE_STATUS.put("TBOX_001", "3"); // 已锁
    DEVICE_STATUS.put("TBOX_002", "3"); // 已锁
    USER_VEHICLE.put("1001", "vehicle_001");
    USER_VEHICLE.put("1002", "vehicle_002");
}
```

## 🔧 立即可用的修复

我已经禁用了数据库自动初始化，现在应该可以启动了。如果还有问题，我们可以：

1. **完全移除数据库依赖**
2. **使用纯内存存储**
3. **只保留TCP和WebSocket核心功能**

## 📱 核心功能测试

不管有没有数据库，4G控车的核心功能都可以工作：

1. **TCP服务器**：监听端口9999，接收TBOX连接
2. **WebSocket服务器**：监听端口9201，接收移动端连接
3. **消息转发**：WebSocket → TCP → TBOX

## 🎯 最简单的验证方法

1. **启动应用**：应该看到"TCP服务器启动成功"
2. **测试TCP**：`telnet localhost 9999`
3. **测试WebSocket**：浏览器控制台连接`ws://localhost:9201/websocket/message?userId=1001`

这样就能验证核心功能是否正常，不需要任何复杂的配置！

## 💡 建议

按照您的要求"最简单的方式"，我建议：
1. 先试试现在的配置（已禁用数据库初始化）
2. 如果还有问题，我们就完全移除数据库依赖
3. 使用纯内存存储，专注于4G控车核心功能

这样可以最快验证系统是否能正常工作！
