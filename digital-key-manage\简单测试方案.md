# 4G控车功能 - 最简单测试方案

## ✅ 问题已修复

### 修复的问题：
1. ✅ **Bean定义冲突** → 已解决
2. ✅ **Nacos连接失败** → 已禁用
3. ✅ **数据库配置问题** → 使用H2内存数据库
4. ✅ **YAML配置错误** → 移除重复的driver-class-name

## 🚀 最简单启动步骤

### 1. 重新启动应用
```bash
# 直接运行主类：
com.ruoyi.system.RuoYiSystemApplication
```

### 2. 确认启动成功
看到以下日志表示成功：
```
TCP服务器启动成功，监听端口: 9999
```

## 📱 最简单测试步骤

### 方案1：使用HTML模拟器（推荐）

1. **打开TBOX模拟器**
   - 浏览器打开：`digital-key-manage/tbox-simulator.html`
   - 点击"连接TCP服务器"

2. **打开移动端模拟器**
   - 浏览器打开：`digital-key-manage/mobile-simulator.html`
   - 点击"连接WebSocket"

3. **测试控车功能**
   - 移动端点击"发送解锁命令"
   - 观察TBOX端是否收到命令

### 方案2：使用命令行测试（更简单）

1. **测试TCP服务器**
   ```bash
   telnet localhost 9999
   # 发送测试消息：7E000A000A000102030405060708090A00003033303230303031303400007E
   ```

2. **测试WebSocket服务器**
   ```bash
   # 浏览器控制台测试：
   ws = new WebSocket('ws://localhost:9201/websocket/message?userId=1001');
   ws.send('{"type":"4G_CONTROL","action":"unlock","vehicleId":"vehicle_001"}');
   ```

## 🎯 预期结果

- **TCP服务器**：端口9999正常监听
- **WebSocket服务器**：端口9201正常工作
- **数据库**：H2内存数据库自动初始化
- **控车流程**：移动端 → WebSocket → TCP → TBOX

## 🔧 如果还有问题

1. **检查端口占用**：
   ```bash
   netstat -ano | findstr :9999
   netstat -ano | findstr :9201
   ```

2. **查看完整日志**：重点关注启动过程中的错误信息

3. **最简单的验证**：
   - 应用启动不报错 = 配置正确
   - 看到"TCP服务器启动成功" = 核心功能可用

## 💡 核心功能说明

这个4G控车系统的核心就是：
- **TCP服务器**：接收TBOX设备连接（端口9999）
- **WebSocket服务器**：接收移动端连接（端口9201）
- **消息转发**：WebSocket收到控车命令后转发给TCP客户端

所有复杂的配置都已经简化，现在只需要启动应用就能测试核心功能！
